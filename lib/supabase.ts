import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY!

export const supabase = createClient(supabaseUrl, supabaseKey)

// Database types
export interface Attorney {
  source_id: string
  title: string
  price?: number
  category_name?: string
  address: string
  neighborhood?: string
  street?: string
  city: string
  postal_code?: string
  state: string
  country_code?: string
  website?: string
  phone?: string
  phone_unformatted?: string
  claim_this_business?: boolean
  lat: number
  lng: number
  total_score: number
  permanently_closed?: boolean
  temporarily_closed?: boolean
  place_id?: string
  categories?: any
  fid?: string
  cid?: string
  reviews_count: number
  images_count?: number
  image_categories?: any
  scraped_at?: string
  google_food_url?: string
  hotel_ads?: any
  opening_hours?: any
  people_also_search?: any
  places_tags?: any
  reviews_tags?: any
  additional_info?: any
  gas_prices?: any
  search_page_url?: string
  search_string?: string
  language?: string
  rank?: number
  is_advertisement?: boolean
  image_url?: string
  kgmid?: string
  url: string
  processed_reviews?: {
    snippets?: Array<{
      id: string
      text: string
      keywords: string[]
      sentiment: string
    }>
    aggregates?: {
      total_reviews: number
      average_rating: number
      sentiment_distribution: {
        positive: number
        negative: number
        neutral: number
      }
    }
  }
  seo_snippets?: Array<{
    id: string
    text: string
    keywords: string[]
    sentiment: string
  }>
  service_keywords?: string[]
  faq_items?: Array<{
    question: string
    answer: string
    source: string
  }>
  review_stats?: any
  reviews_processed?: boolean
  last_reviews_update?: string
}

// Database query functions
export async function getAttorneys(
  page: number = 1,
  limit: number = 20,
  city?: string,
  state?: string,
  searchTerm?: string
) {
  try {
    let query = supabase
      .from('findcaraccidentattorneys-clean')
      .select('*', { count: 'exact' })
      .eq('reviews_processed', true)
      .order('total_score', { ascending: false })
      .order('reviews_count', { ascending: false })

    if (city) {
      query = query.ilike('city', `%${city}%`)
    }

    if (state) {
      query = query.ilike('state', `%${state}%`)
    }

    if (searchTerm) {
      query = query.or(`title.ilike.%${searchTerm}%,city.ilike.%${searchTerm}%,state.ilike.%${searchTerm}%`)
    }

    const from = (page - 1) * limit
    const to = from + limit - 1

    const { data, error, count } = await query.range(from, to)

    if (error) {
      console.error('Error fetching attorneys:', error)
      return { attorneys: [], total: 0, error: error.message }
    }

    return {
      attorneys: data as Attorney[],
      total: count || 0,
      error: null
    }
  } catch (error) {
    console.error('Unexpected error in getAttorneys:', error)
    return { attorneys: [], total: 0, error: 'Unexpected error' }
  }
}

export async function getAttorneyBySlug(slug: string) {
  const { data, error } = await supabase
    .from('findcaraccidentattorneys-clean')
    .select('*')
    .eq('source_id', slug)
    .eq('reviews_processed', true)
    .single()

  if (error) {
    console.error('Error fetching attorney:', error)
    return { attorney: null, error: error.message }
  }

  return { attorney: data as Attorney, error: null }
}

export async function getStates() {
  const { data, error } = await supabase
    .from('findcaraccidentattorneys-clean')
    .select('state')
    .eq('reviews_processed', true)
    .not('state', 'is', null)

  if (error) {
    console.error('Error fetching states:', error)
    return { states: [], error: error.message }
  }

  const uniqueStates = [...new Set(data.map(item => item.state))].sort()
  return { states: uniqueStates, error: null }
}

export async function getCitiesByState(state: string) {
  const { data, error } = await supabase
    .from('findcaraccidentattorneys-clean')
    .select('city')
    .eq('reviews_processed', true)
    .ilike('state', `%${state}%`)
    .not('city', 'is', null)

  if (error) {
    console.error('Error fetching cities:', error)
    return { cities: [], error: error.message }
  }

  const uniqueCities = [...new Set(data.map(item => item.city))].sort()
  return { cities: uniqueCities, error: null }
}

export async function getAttorneysByLocation(state: string, city?: string) {
  let query = supabase
    .from('findcaraccidentattorneys-clean')
    .select('*')
    .eq('reviews_processed', true)
    .ilike('state', `%${state}%`)
    .order('total_score', { ascending: false })
    .order('reviews_count', { ascending: false })

  if (city) {
    query = query.ilike('city', `%${city}%`)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching attorneys by location:', error)
    return { attorneys: [], error: error.message }
  }

  return { attorneys: data as Attorney[], error: null }
}
